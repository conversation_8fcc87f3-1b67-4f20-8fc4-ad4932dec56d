<template>
  <div class="form-container" overflow-y-auto overflow-x-hidden>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      class="typical-form"
      label-position="left"
      label-width="110px"
      w-full
      mt-10
    >
      <div class="half-col">
        <el-form-item label="账号名称" prop="accountName">
          <el-input v-model.trim="formData.accountName" placeholder="账号名称" clearable>
            <template #prefix>
              <i class="iconfont icon-instruction"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="所属机构" prop="orgId">
          <el-select
            v-model="formData.orgId"
            placeholder="请选择所属机构"
            :disabled="!hasGlobalDataScope"
            filterable
            clearable
            @change="handleOrgChange"
          >
            <el-option
              v-for="(item, idx) in myOrgs"
              :key="idx"
              :label="item.orgName"
              :value="item.id"
            />
            <template #prefix>
              <i class="iconfont icon-building"></i>
            </template>
          </el-select>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="所属产品" prop="funds">
          <el-select
            v-model="states.fundId"
            @change="handleFundChange"
            placeholder="请选择所属产品"
            filterable
            clearable
          >
            <el-option
              v-for="(item, idx) in myFunds"
              :key="idx"
              :label="item.fundName"
              :value="item.fundId"
            />
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-select>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="资产类型" prop="assetType">
          <el-select
            v-model="formData.assetType"
            @change="handleAssetTypeChange"
            placeholder="请选择资产类型"
            filterable
            clearable
          >
            <el-option
              v-for="(item, idx) in AssetTypes"
              :key="idx"
              :label="item.Label"
              :value="item.Value"
            />
            <template #prefix>
              <i class="iconfont icon-block"></i>
            </template>
          </el-select>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item label="账号类型" prop="credit">
          <el-select v-model="formData.credit" placeholder="请选择账号类型" filterable clearable>
            <el-option label="普通账号" :value="false" />
            <el-option label="信用账号" :value="true" />
            <template #prefix>
              <i class="iconfont icon-strategy"></i>
            </template>
          </el-select>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="经纪商" prop="bkId">
          <el-select
            v-model="formData.bkId"
            placeholder="请选择经纪商"
            @change="handleBrokerChange"
            filterable
            clearable
          >
            <el-option
              v-for="(item, idx) in typeBrokers"
              :key="idx"
              :label="item.brokerName"
              :value="item.id"
            />
            <template #prefix>
              <i class="iconfont icon-user"></i>
            </template>
          </el-select>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item label="营业部代码" prop="extInfo.yybId">
          <el-input v-model.trim="formData.extInfo.yybId" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item label="沪市股东代码" prop="extInfo.shSecId">
          <el-input v-model.trim="formData.extInfo.shSecId" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item label="深市股东代码" prop="extInfo.szSecId">
          <el-input v-model.trim="formData.extInfo.szSecId" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="资金账号" prop="financeAccount">
          <el-input v-model.trim="formData.financeAccount" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item label="交易账号" prop="extInfo.tradeAccount">
          <el-input v-model.trim="formData.extInfo.tradeAccount" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isFuture" class="half-col">
        <el-form-item label="APPID" prop="extInfo.tradeAccount">
          <el-input v-model.trim="formData.extInfo.tradeAccount" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-document-code"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="账号密码" prop="pwd">
          <el-input type="password" v-model.trim="formData.pwd" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-password"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div v-if="isStock" class="half-col">
        <el-form-item :label="isStock ? '通信密码' : 'AUTH CODE'" prop="extInfo.txPassword">
          <el-input type="password" v-model.trim="formData.extInfo.txPassword" clearable>
            <template #prefix>
              <i fs-18 class="iconfont icon-password"></i>
            </template>
          </el-input>
        </el-form-item>
      </div>
      <div class="half-col">
        <el-form-item label="交易终端" prop="terminals">
          <el-select
            v-model="states.terminals"
            placeholder="请选择交易终端"
            multiple
            collapse-tags
            filterable
            clearable
          >
            <el-option
              v-for="(item, idx) in typeTerminals"
              :key="idx"
              :label="item.terminalName"
              :value="item.id"
            />
            <template #prefix>
              <i class="iconfont icon-exchange"></i>
            </template>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
  </div>
  <div class="typical-dialog-footer" flex jcc gap-16 pt-16 pb-4>
    <el-button @click="cancel" w-200>取消</el-button>
    <el-button v-if="isEdit && canTestConnection" type="success" @click="testConn" w-220>
      连通性测试
    </el-button>
    <el-button type="primary" @click="check" w-220>确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, shallowRef, useTemplateRef } from 'vue';
import { type AccountInfo, type ProductInfo } from '@/types';
import { deepClone, getUser, hasGlobalDataPermission, isNone, hasPermission } from '@/script';
import { ElMessage } from 'element-plus';
import { MenuPermitAccountManagement } from '@/enum';

import {
  AssetType,
  CreateEmptyAccountRecord,
  Repos,
  TerminalType,
  type LegacyAccountInfo,
  type MomBroker,
  type MomOrganization,
  type MomTerminal,
} from '../../../../xtrade-sdk/dist';

const { contextProduct } = defineProps<{
  /** 关联的产品，用于账号创建时回填 */
  contextProduct?: ProductInfo | null;
}>();

const formRef = useTemplateRef('formRef');
const TerminalTypes = Object.values(TerminalType);
const AssetTypes = [AssetType.Stock, AssetType.Future, AssetType.Option];
const repoInstance = new Repos.AdminRepo();
const repoGovInstance = new Repos.GovernanceRepo();
const hasGlobalDataScope = ref(hasGlobalDataPermission());
const formData = ref<AccountInfo>(CreateEmptyAccountRecord());

const currentUser = getUser()!;
const states = reactive({
  terminals: [] as number[],
  fundId: null as any,
});

const rules = {
  accountName: [{ required: true, message: '请输入账号名称', trigger: 'blur' }],
  orgId: [{ required: true, message: '请选择所属机构', trigger: 'blur' }],
  funds: [{ required: true, message: '请选择所属产品', trigger: 'blur' }],
  assetType: [{ required: true, message: '请选择资产类型', trigger: 'blur' }],
  credit: [{ required: true, message: '请选择账号类型', trigger: 'blur' }],
  bkId: [{ required: true, message: '请选择经纪商', trigger: 'blur' }],
  financeAccount: [{ required: true, message: '请输入资金账号', trigger: 'blur' }],
  // pwd: { required: true, message: '请输入资金账号密码' },
  'extInfo.tradeAccount': [{ required: true, message: '请输入交易账号', trigger: 'blur' }],
  'extInfo.yybId': [{ required: true, message: '请输入营业部代码', trigger: 'blur' }],
  'extInfo.shSecId': [{ required: true, message: '请输入沪市股东代码', trigger: 'blur' }],
  'extInfo.szSecId': [{ required: true, message: '请输入深市股东代码', trigger: 'blur' }],
};

const isStock = computed(() => {
  return formData.value.assetType == AssetType.Stock.Value;
});

const isFuture = computed(() => {
  return formData.value.assetType == AssetType.Future.Value;
});

const isEdit = computed(() => {
  return !!formData.value.id;
});

const canTestConnection = computed(() => {
  return hasPermission(MenuPermitAccountManagement.连通性测试);
});

const emitter = defineEmits<{
  cancel: [];
  save: [data: LegacyAccountInfo];
}>();

async function testConn() {
  const { errorCode, errorMsg } = await repoGovInstance.ConnectAccount(formData.value.id);
  if (errorCode === 0) {
    ElMessage.success('测试结果：连接成功');
  } else {
    ElMessage.error(errorMsg || '未能连接');
  }
}

const cancel = () => {
  emitter('cancel');
  formRef.value?.clearValidate();
};

const check = () => {
  formRef.value?.validate(valid => {
    if (valid) {
      save();
    }
  });
};

function save() {
  const obj = formData.value;
  const list = terminals.value.filter(x => states.terminals.includes(x.id));

  // 选择的终端列表（保存成功后，需单独再绑定终端）
  obj.terminals = list.map(x => {
    const { id, interfaceType, status, terminalName } = x;
    return {
      id,
      interfaceType,
      status,
      terminalName,
    };
  });

  // 选择所属产品
  const matched = myFunds.value.find(x => x.fundId == states.fundId);
  obj.funds = deepClone(matched ? [matched] : []);
  submitData(deepClone(obj));
}

async function submitData(row: LegacyAccountInfo) {
  const isModify = !!row.id;
  const behavior = isModify ? '修改' : '创建';

  if (isNone(row.orgId)) {
    // 补充必要的字段
    row.orgId = currentUser.orgId;
    row.orgName = currentUser.orgName;
  }

  const { errorCode, errorMsg, data } = isModify
    ? await repoGovInstance.UpdateAccount(row)
    : await repoGovInstance.CreateAccount(row);

  if (errorCode === 0) {
    ElMessage.success(`${behavior}成功`);
    const account_id = data!.id;
    const terminal_ids = row.terminals.map(x => x.id);
    if (terminal_ids.length > 0) {
      const { errorCode, errorMsg } = await repoGovInstance.BindAccountTerminals(
        account_id,
        terminal_ids,
      );
      if (errorCode === 0) {
        ElMessage.success('终端绑定成功');
        emitter('save', data!);
      } else {
        ElMessage.error(errorMsg || '终端绑定失败');
      }
    } else {
      emitter('save', data!);
    }
  } else {
    ElMessage.error(errorMsg || `${behavior}失败`);
  }
}

function handleFundChange() {
  // 选择所属产品
  const matched = myFunds.value.find(x => x.fundId == states.fundId);
  formData.value.funds = deepClone(matched ? [matched] : []);
}

function handleAssetTypeChange() {
  const obj = formData.value;
  const newVal = obj.assetType;

  if (newVal != AssetType.Stock.Value) {
    obj.credit = false;
  }

  obj.bkId = null as any;
  obj.brokerId = null as any;
  states.terminals = [];
}

const orgs = shallowRef<MomOrganization[]>([]);
const myOrgs = computed(() => {
  return hasGlobalDataScope.value ? orgs.value : orgs.value.filter(x => x.id == currentUser.orgId);
});

const borkers = shallowRef<MomBroker[]>([]);
const terminals = shallowRef<MomTerminal[]>([]);
const funds = shallowRef<{ orgId: number; fundId: string; fundName: string }[]>([]);

const myFunds = computed(() => {
  const all = funds.value;
  const belongs = hasGlobalDataScope.value ? all : all.filter(x => x.orgId == currentUser.orgId);
  return belongs.map(x => {
    const { fundId, fundName } = x;
    return { fundId, fundName };
  });
});

const typeBrokers = computed(() => {
  return borkers.value.filter(x => x.brokerType == formData.value.assetType);
});

const typeTerminals = computed(() => {
  const iftype =
    TerminalTypes.find(x => x.AssetType == formData.value.assetType)?.Value ||
    TerminalTypes[0].Value;
  return terminals.value.filter(x => x.interfaceType == iftype);
});

function handleBrokerChange() {
  const matched = borkers.value.find(x => x.id == formData.value.bkId);
  formData.value.brokerId = matched?.brokerId || '';
  formData.value.brokerName = matched?.brokerName || '';
}

function handleOrgChange() {
  const matched = myOrgs.value.find(x => x.id == formData.value.orgId);
  formData.value.orgName = matched?.orgName || '';
}

async function requestBrokers() {
  borkers.value = (await repoInstance.QueryBrokers()).data || [];
}

async function requestTerminals() {
  terminals.value = (await repoInstance.QueryTerminals()).data || [];
}

async function requestOrgs() {
  orgs.value = (await repoInstance.QueryOrgs()).data || [];
}

async function requestProducts() {
  const list = (await repoGovInstance.QueryProducts()).data || [];
  funds.value = list.map(x => ({ orgId: x.orgId, fundId: x.id, fundName: x.fundName }));
}

async function reset(context: AccountInfo | null) {
  const obj = (formData.value = context ? deepClone(context) : CreateEmptyAccountRecord());
  states.terminals = obj.terminals.map(x => x.id);
  states.fundId = obj.funds.length > 0 ? obj.funds[0].fundId : null;

  requestBrokers();
  requestTerminals();
  requestOrgs();
  await requestProducts();
  if (contextProduct) {
    formData.value.orgId = contextProduct.orgId;
    formData.value.orgName = contextProduct.orgName;
    states.fundId = contextProduct.id;
    handleFundChange();
  }
}

defineExpose({
  reset,
});

onMounted(() => {
  //
});
</script>

<style scoped>
.form-container {
  .half-col {
    float: left;
    width: 50%;
  }
  :deep() {
    .el-form-item__label {
      position: relative;
    }
    .el-form-item {
      width: 95%;
      margin-right: 10px !important;
    }
  }
}
</style>
